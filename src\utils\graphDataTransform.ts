/**
 * 图数据转换工具
 * 用于将原始血缘数据转换为G6可用的节点和边数据
 */

import type {
  LineageData,
  G6GraphData,
  G6NodeData,
  G6EdgeData,
  TableInfo,
  LineageNode,
  LineageEdge
} from '@/types/lineage'

/**
 * 将血缘数据转换为G6图数据
 * @param lineageData 原始血缘数据
 * @returns G6图数据
 */
export function transformToG6Data(lineageData: LineageData): G6GraphData {
  const nodes: G6NodeData[] = []
  const edges: G6EdgeData[] = []

  // 转换表节点
  Object.values(lineageData.tables).forEach((table: TableInfo) => {
    const nodeData: G6NodeData = {
      id: table.name,
      type: 'table-node',
      x: table.position?.x,
      y: table.position?.y,
      tableName: table.name,
      fields: table.fields,
      tableInfo: table,
      label: table.name,
      labelCfg: {
        position: 'top',
        offset: 10
      }
    }
    nodes.push(nodeData)
  })

  // 转换边数据 - 支持字段级连接
  lineageData.edges.forEach((edge) => {
    const sourceTableName = getTableNameFromFieldId(edge.source)
    const targetTableName = getTableNameFromFieldId(edge.target)
    const sourceFieldName = getFieldNameFromFieldId(edge.source)
    const targetFieldName = getFieldNameFromFieldId(edge.target)

    const edgeData: G6EdgeData = {
      id: edge.id,
      source: sourceTableName,
      target: targetTableName,
      type: 'field-edge',
      lineageEdge: edge,
      label: edge.label,
      // 添加字段级连接的额外信息
      sourceField: sourceFieldName,
      targetField: targetFieldName,
      sourceFieldId: edge.source,
      targetFieldId: edge.target,
      transformType: edge.transformType,
      confidence: edge.confidence,
      expression: edge.expression,
      style: {
        // 根据转换类型设置不同的样式
        stroke: getEdgeColorByTransformType(edge.transformType),
        lineWidth: getEdgeWidthByConfidence(edge.confidence),
        lineDash: getEdgeDashByTransformType(edge.transformType)
      },
      labelCfg: {
        autoRotate: true,
        style: {
          fontSize: 10,
          fill: '#666',
          background: {
            fill: '#ffffff',
            padding: [2, 4],
            radius: 2
          }
        }
      }
    }
    edges.push(edgeData)
  })

  return { nodes, edges }
}

/**
 * 从字段ID中提取表名
 * @param fieldId 字段ID，格式：表名.字段名
 * @returns 表名
 */
export function getTableNameFromFieldId(fieldId: string): string {
  return fieldId.split('.')[0]
}

/**
 * 从字段ID中提取字段名
 * @param fieldId 字段ID，格式：表名.字段名
 * @returns 字段名
 */
export function getFieldNameFromFieldId(fieldId: string): string {
  return fieldId.split('.').slice(1).join('.')
}

/**
 * 生成字段ID
 * @param tableName 表名
 * @param fieldName 字段名
 * @returns 字段ID
 */
export function generateFieldId(tableName: string, fieldName: string): string {
  return `${tableName}.${fieldName}`
}

/**
 * 计算表节点的尺寸
 * @param fields 字段列表
 * @returns 节点尺寸 [width, height]
 */
export function calculateNodeSize(fields: LineageNode[]): [number, number] {
  const minWidth = 200
  const minHeight = 100
  const fieldHeight = 24
  const headerHeight = 40
  const padding = 16

  // 计算最长字段名的宽度（简单估算）
  const maxFieldNameLength = Math.max(
    ...fields.map(field => field.fieldName.length)
  )
  const estimatedWidth = Math.max(minWidth, maxFieldNameLength * 8 + padding * 2)

  // 计算高度
  const estimatedHeight = Math.max(
    minHeight,
    headerHeight + fields.length * fieldHeight + padding
  )

  return [estimatedWidth, estimatedHeight]
}

/**
 * 根据字段类型获取颜色
 * @param dataType 数据类型
 * @returns 颜色值
 */
export function getFieldTypeColor(dataType?: string): string {
  if (!dataType) return '#666'

  const type = dataType.toLowerCase()

  if (type.includes('int') || type.includes('number') || type.includes('decimal')) {
    return '#1890ff' // 蓝色 - 数值类型
  }
  if (type.includes('varchar') || type.includes('char') || type.includes('text')) {
    return '#52c41a' // 绿色 - 字符类型
  }
  if (type.includes('date') || type.includes('time') || type.includes('timestamp')) {
    return '#fa8c16' // 橙色 - 时间类型
  }
  if (type.includes('bool') || type.includes('bit')) {
    return '#eb2f96' // 粉色 - 布尔类型
  }

  return '#666' // 默认灰色
}

/**
 * 格式化数据类型显示
 * @param dataType 数据类型对象
 * @returns 格式化后的类型字符串
 */
export function formatDataType(dataType: any): string {
  if (!dataType) return 'unknown'

  if (typeof dataType === 'string') {
    return dataType
  }

  let result = dataType.type || 'unknown'

  if (dataType.length) {
    result += `(${dataType.length}`
    if (dataType.precision && dataType.scale) {
      result += `,${dataType.scale}`
    }
    result += ')'
  } else if (dataType.precision) {
    result += `(${dataType.precision}`
    if (dataType.scale) {
      result += `,${dataType.scale}`
    }
    result += ')'
  }

  return result
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID
 */
export function generateUniqueId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 验证血缘数据的完整性
 * @param lineageData 血缘数据
 * @returns 验证结果
 */
export function validateLineageData(lineageData: LineageData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = []
  const warnings: string[] = []

  // 检查基本结构
  if (!lineageData) {
    errors.push('血缘数据不能为空')
    return { isValid: false, errors, warnings }
  }

  if (!lineageData.tables || Object.keys(lineageData.tables).length === 0) {
    errors.push('至少需要一个表')
  }

  if (!lineageData.nodes || lineageData.nodes.length === 0) {
    warnings.push('没有字段节点数据')
  }

  if (!lineageData.edges || lineageData.edges.length === 0) {
    warnings.push('没有血缘关系边数据')
  }

  // 检查表数据完整性
  Object.values(lineageData.tables).forEach((table, index) => {
    if (!table.name) {
      errors.push(`表${index}缺少名称`)
    }

    if (!table.fields || table.fields.length === 0) {
      warnings.push(`表${table.name}没有字段`)
    } else {
      // 检查字段数据完整性
      table.fields.forEach((field, fieldIndex) => {
        if (!field.id) {
          errors.push(`表${table.name}的字段${fieldIndex}缺少ID`)
        }
        if (!field.fieldName) {
          errors.push(`表${table.name}的字段${fieldIndex}缺少字段名`)
        }
        if (field.tableName !== table.name) {
          warnings.push(`表${table.name}的字段${field.fieldName}的tableName不匹配`)
        }
      })
    }
  })

  // 检查边数据完整性
  lineageData.edges.forEach((edge, index) => {
    if (!edge.id) {
      errors.push(`边${index}缺少ID`)
    }
    if (!edge.source) {
      errors.push(`边${edge.id || index}缺少源字段ID`)
    }
    if (!edge.target) {
      errors.push(`边${edge.id || index}缺少目标字段ID`)
    }

    // 检查源和目标字段是否存在
    const sourceExists = lineageData.nodes.some(node => node.id === edge.source)
    const targetExists = lineageData.nodes.some(node => node.id === edge.target)

    if (!sourceExists) {
      warnings.push(`边${edge.id}的源字段${edge.source}不存在`)
    }
    if (!targetExists) {
      warnings.push(`边${edge.id}的目标字段${edge.target}不存在`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 根据转换类型获取边颜色
 * @param transformType 转换类型
 * @returns 颜色值
 */
export function getEdgeColorByTransformType(transformType?: string): string {
  const colorMap: Record<string, string> = {
    'DIRECT': '#1890ff',      // 直接映射 - 蓝色
    'JOIN': '#52c41a',        // 连接 - 绿色
    'AGGREGATE': '#fa8c16',   // 聚合 - 橙色
    'FILTER': '#722ed1',      // 过滤 - 紫色
    'TRANSFORM': '#eb2f96',   // 转换 - 粉色
    'UNION': '#13c2c2',       // 联合 - 青色
    'WINDOW': '#faad14'       // 窗口函数 - 黄色
  }

  return colorMap[transformType || 'DIRECT'] || '#1890ff'
}

/**
 * 根据置信度获取边宽度
 * @param confidence 置信度 (0-1)
 * @returns 线宽
 */
export function getEdgeWidthByConfidence(confidence?: number): number {
  if (confidence === undefined) return 2

  if (confidence >= 0.9) return 3
  if (confidence >= 0.7) return 2
  if (confidence >= 0.5) return 1.5
  return 1
}

/**
 * 根据转换类型获取边虚线样式
 * @param transformType 转换类型
 * @returns 虚线数组
 */
export function getEdgeDashByTransformType(transformType?: string): number[] | undefined {
  const dashMap: Record<string, number[]> = {
    'DIRECT': [],             // 实线
    'JOIN': [],               // 实线
    'AGGREGATE': [],          // 实线
    'FILTER': [5, 5],         // 虚线
    'TRANSFORM': [3, 3],      // 短虚线
    'UNION': [8, 4],          // 长虚线
    'WINDOW': [2, 2, 8, 2]    // 点划线
  }

  const dash = dashMap[transformType || 'DIRECT']
  return dash && dash.length > 0 ? dash : undefined
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func.apply(null, args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  let previous = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    const remaining = wait - (now - previous)

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func.apply(null, args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func.apply(null, args)
      }, remaining)
    }
  }
}

/**
 * 从原始JSON数据转换为标准血缘数据格式
 * @param rawData 原始JSON数据
 * @returns 标准血缘数据
 */
export function transformRawDataToLineageData(rawData: any): LineageData {
  // 如果已经是标准格式，直接返回
  if (rawData.tables && rawData.nodes && rawData.edges) {
    return rawData as LineageData
  }

  const tables: { [key: string]: TableInfo } = {}
  const nodes: LineageNode[] = []
  const edges: LineageEdge[] = []

  // 处理不同格式的原始数据
  if (Array.isArray(rawData)) {
    // 如果是数组格式，假设是表的数组
    rawData.forEach((tableData: any, index: number) => {
      const tableName = tableData.name || tableData.tableName || `table_${index}`
      const table = processTableData(tableData, tableName)
      tables[tableName] = table
      nodes.push(...table.fields)
    })
  } else if (rawData.tables) {
    // 如果有tables字段
    if (Array.isArray(rawData.tables)) {
      rawData.tables.forEach((tableData: any) => {
        const tableName = tableData.name || tableData.tableName
        if (tableName) {
          const table = processTableData(tableData, tableName)
          tables[tableName] = table
          nodes.push(...table.fields)
        }
      })
    } else {
      // tables是对象格式
      Object.entries(rawData.tables).forEach(([tableName, tableData]: [string, any]) => {
        const table = processTableData(tableData, tableName)
        tables[tableName] = table
        nodes.push(...table.fields)
      })
    }
  }

  // 处理边数据
  if (rawData.edges && Array.isArray(rawData.edges)) {
    rawData.edges.forEach((edgeData: any) => {
      const edge = processEdgeData(edgeData)
      if (edge) {
        edges.push(edge)
      }
    })
  } else if (rawData.relationships && Array.isArray(rawData.relationships)) {
    rawData.relationships.forEach((edgeData: any) => {
      const edge = processEdgeData(edgeData)
      if (edge) {
        edges.push(edge)
      }
    })
  }

  return {
    tables,
    nodes,
    edges,
    metadata: {
      sqlText: rawData.sql || rawData.sqlText || '',
      parseTime: new Date().toISOString(),
      version: '1.0.0'
    }
  }
}

/**
 * 处理表数据
 * @param tableData 原始表数据
 * @param tableName 表名
 * @returns 标准表信息
 */
function processTableData(tableData: any, tableName: string): TableInfo {
  const fields: LineageNode[] = []

  // 处理字段数据
  const fieldsData = tableData.fields || tableData.columns || []
  fieldsData.forEach((fieldData: any, index: number) => {
    const fieldName = fieldData.name || fieldData.fieldName || fieldData.columnName || `field_${index}`
    const field: LineageNode = {
      id: `${tableName}.${fieldName}`,
      label: fieldName,
      tableName,
      fieldName,
      type: 'field',
      dataType: fieldData.dataType || fieldData.type || { type: 'VARCHAR' },
      description: fieldData.description || fieldData.comment,
      isKey: fieldData.isKey || fieldData.isPrimaryKey || false,
      isNullable: fieldData.isNullable !== false,
      defaultValue: fieldData.defaultValue
    }
    fields.push(field)
  })

  return {
    name: tableName,
    type: tableData.type || 'table',
    fields,
    description: tableData.description || tableData.comment,
    position: tableData.position,
    rowCount: tableData.rowCount,
    size: tableData.size
  }
}

/**
 * 处理边数据
 * @param edgeData 原始边数据
 * @returns 标准边信息
 */
function processEdgeData(edgeData: any): LineageEdge | null {
  if (!edgeData.source || !edgeData.target) {
    return null
  }

  return {
    id: edgeData.id || generateUniqueId('edge'),
    source: edgeData.source || edgeData.sourceField,
    target: edgeData.target || edgeData.targetField,
    label: edgeData.label || edgeData.type,
    transformType: edgeData.transformType || edgeData.type || 'DIRECT',
    expression: edgeData.expression || edgeData.sql,
    confidence: edgeData.confidence || 1.0
  }
}

/**
 * 优化图数据布局
 * @param g6Data G6图数据
 * @param options 布局选项
 * @returns 优化后的G6图数据
 */
export function optimizeGraphLayout(
  g6Data: G6GraphData,
  options: {
    nodeSpacing?: number;
    rankSpacing?: number;
    direction?: 'LR' | 'TB' | 'RL' | 'BT';
  } = {}
): G6GraphData {
  const { nodeSpacing = 80, rankSpacing = 150, direction = 'LR' } = options

  // 创建深拷贝避免修改原数据
  const optimizedData = deepClone(g6Data)

  // 如果没有位置信息，使用简单的层次布局
  if (optimizedData.nodes.some(node => !node.x || !node.y)) {
    const layers = calculateNodeLayers(optimizedData)
    applyLayeredLayout(optimizedData, layers, { nodeSpacing, rankSpacing, direction })
  }

  return optimizedData
}

/**
 * 计算节点层次
 * @param g6Data G6图数据
 * @returns 节点层次映射
 */
function calculateNodeLayers(g6Data: G6GraphData): Map<string, number> {
  const layers = new Map<string, number>()
  const visited = new Set<string>()
  const inDegree = new Map<string, number>()

  // 计算入度
  g6Data.nodes.forEach(node => {
    inDegree.set(node.id, 0)
  })

  g6Data.edges.forEach(edge => {
    const targetDegree = inDegree.get(edge.target) || 0
    inDegree.set(edge.target, targetDegree + 1)
  })

  // 拓扑排序分层
  let currentLayer = 0
  let queue = g6Data.nodes.filter(node => (inDegree.get(node.id) || 0) === 0)

  while (queue.length > 0) {
    const nextQueue: any[] = []

    queue.forEach(node => {
      layers.set(node.id, currentLayer)
      visited.add(node.id)

      // 找到所有出边的目标节点
      g6Data.edges.forEach(edge => {
        if (edge.source === node.id) {
          const targetDegree = inDegree.get(edge.target) || 0
          inDegree.set(edge.target, targetDegree - 1)

          if (inDegree.get(edge.target) === 0 && !visited.has(edge.target)) {
            const targetNode = g6Data.nodes.find(n => n.id === edge.target)
            if (targetNode && !nextQueue.includes(targetNode)) {
              nextQueue.push(targetNode)
            }
          }
        }
      })
    })

    queue = nextQueue
    currentLayer++
  }

  return layers
}

/**
 * 应用分层布局
 * @param g6Data G6图数据
 * @param layers 节点层次映射
 * @param options 布局选项
 */
function applyLayeredLayout(
  g6Data: G6GraphData,
  layers: Map<string, number>,
  options: {
    nodeSpacing: number;
    rankSpacing: number;
    direction: 'LR' | 'TB' | 'RL' | 'BT';
  }
): void {
  const { nodeSpacing, rankSpacing, direction } = options
  const layerNodes = new Map<number, any[]>()

  // 按层分组节点
  g6Data.nodes.forEach(node => {
    const layer = layers.get(node.id) || 0
    if (!layerNodes.has(layer)) {
      layerNodes.set(layer, [])
    }
    layerNodes.get(layer)!.push(node)
  })

  // 为每层的节点分配位置
  layerNodes.forEach((nodes, layer) => {
    nodes.forEach((node, index) => {
      const [nodeWidth, nodeHeight] = calculateNodeSize(node.fields || [])

      switch (direction) {
        case 'LR': // 从左到右
          node.x = layer * rankSpacing
          node.y = (index - (nodes.length - 1) / 2) * (nodeHeight + nodeSpacing)
          break
        case 'TB': // 从上到下
          node.x = (index - (nodes.length - 1) / 2) * (nodeWidth + nodeSpacing)
          node.y = layer * rankSpacing
          break
        case 'RL': // 从右到左
          node.x = -layer * rankSpacing
          node.y = (index - (nodes.length - 1) / 2) * (nodeHeight + nodeSpacing)
          break
        case 'BT': // 从下到上
          node.x = (index - (nodes.length - 1) / 2) * (nodeWidth + nodeSpacing)
          node.y = -layer * rankSpacing
          break
      }
    })
  })
}

/**
 * 数据统计信息
 * @param lineageData 血缘数据
 * @returns 统计信息
 */
export function getDataStatistics(lineageData: LineageData): {
  tableCount: number;
  fieldCount: number;
  edgeCount: number;
  maxFieldsPerTable: number;
  avgFieldsPerTable: number;
  transformTypes: Record<string, number>;
  confidenceDistribution: {
    high: number; // >= 0.8
    medium: number; // 0.5 - 0.8
    low: number; // < 0.5
  };
} {
  const tableCount = Object.keys(lineageData.tables).length
  const fieldCount = lineageData.nodes.length
  const edgeCount = lineageData.edges.length

  // 计算每个表的字段数
  const fieldsPerTable = Object.values(lineageData.tables).map(table => table.fields.length)
  const maxFieldsPerTable = Math.max(...fieldsPerTable, 0)
  const avgFieldsPerTable = fieldsPerTable.length > 0
    ? fieldsPerTable.reduce((sum, count) => sum + count, 0) / fieldsPerTable.length
    : 0

  // 统计转换类型
  const transformTypes: Record<string, number> = {}
  lineageData.edges.forEach(edge => {
    const type = edge.transformType || 'DIRECT'
    transformTypes[type] = (transformTypes[type] || 0) + 1
  })

  // 统计置信度分布
  const confidenceDistribution = {
    high: 0,
    medium: 0,
    low: 0
  }

  lineageData.edges.forEach(edge => {
    const confidence = edge.confidence || 1.0
    if (confidence >= 0.8) {
      confidenceDistribution.high++
    } else if (confidence >= 0.5) {
      confidenceDistribution.medium++
    } else {
      confidenceDistribution.low++
    }
  })

  return {
    tableCount,
    fieldCount,
    edgeCount,
    maxFieldsPerTable,
    avgFieldsPerTable: Math.round(avgFieldsPerTable * 100) / 100,
    transformTypes,
    confidenceDistribution
  }
}
